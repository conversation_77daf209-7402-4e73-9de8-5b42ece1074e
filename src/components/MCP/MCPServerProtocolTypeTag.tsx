import {Tag} from '@panda-design/components';
import {useMemo} from 'react';
import {MCPServerProtocolType} from '@/types/mcp/mcp';
import {IconSse, IconStdio} from '@/icons/mcp';

export default function MCPServerProtocolTypeTag({type}: {type: MCPServerProtocolType}) {
    const icon = useMemo(
        () => {
            switch (type) {
                case 'SSE':
                    return <IconSse />;
                case 'STDIO':
                    return <IconStdio />;
                case 'Streamable_HTTP':
                    return <IconStdio />;
            }
        },
        [type]
    );
    return (
        <Tag
            type="flat"
            style={{
                color: '#C9661D',
                backgroundColor: '#F8E6DD',
                margin: 0,
            }}
            icon={icon}
        >
            {type === 'Streamable_HTTP' ? 'Streamable HTTP' : type}
        </Tag>
    );
}
