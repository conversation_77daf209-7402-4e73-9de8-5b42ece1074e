import {Flex} from 'antd';
import {IconLocalMcp, IconRemoteMcp, IconStdioMcp} from '@/icons/mcp';
import CreateMCPItem from './CreateMCPItem';

const CreateMcpList = [
    {
        name: '已有MCP（标准MCP）',
        description: '根据分类 （SSE、STDIO、Streamable HTTP）填写Server Config即可完成新建',
        type: 'external',
        icon: <IconStdioMcp />,
    },
    {
        name: 'OpenAPI转MCP（Remote）',
        description: '支持从openapi/swagger文件、iAPI项目中导入API的定义，然后从API List中选择要导入做为工具的API',
        type: 'openapi',
        icon: <IconRemoteMcp />,
    },
    {
        name: '脚本转换MCP（Local） ',
        description: '从icode、irepo导入脚本，或输入并执行CMD，完成新建',
        type: 'script',
        icon: <IconLocalMcp />,
    },
];
const CreateMCPContent = () => {
    return (
        <Flex vertical gap={4}>
            {CreateMcpList.map(item => {
                return <CreateMCPItem key={item.type} {...item} />;
            })}
        </Flex>
    );
};

export default CreateMCPContent;
