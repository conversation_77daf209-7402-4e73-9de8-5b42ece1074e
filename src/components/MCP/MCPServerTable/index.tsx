/* eslint-disable max-lines */
import {Flex, Table, Typography} from 'antd';
import {useRequest} from 'huse';
import {memo, useMemo} from 'react';
import {ColumnType, TableProps} from 'antd/es/table';
import {Button} from '@panda-design/components';
import {difference} from 'lodash';
import {apiGetDefaultLabels} from '@/api/mcp';
import {MCPServerBase, MCPServerProtocolType, SpaceLabel} from '@/types/mcp/mcp';
import {MCPDetailLink} from '@/links/mcp';
import {IconDetail} from '@/icons/mcp';
import TagGroup from '../TagGroup';
import MCPServerProtocolTypeTag from '../MCPServerProtocolTypeTag';

interface Props {
    hasProtocolFilter?: boolean;
    dataSource: MCPServerBase[];
    selectedRowKeys: number[];
    onSelect: (selectedRowKeys: number[]) => void;
    pagination: TableProps<MCPServerBase>['pagination'];
    onChange: TableProps<MCPServerBase>['onChange'];
    scroll?: TableProps<MCPServerBase>['scroll'];
}
function MCPServerTable({
    hasProtocolFilter,
    dataSource,
    selectedRowKeys,
    onSelect,
    pagination,
    onChange,
    scroll,
}: Props) {
    const {data: labels} = useRequest(apiGetDefaultLabels, undefined);
    const columns: Array<ColumnType<MCPServerBase>> = useMemo(
        () => [
            {
                title: 'MCP名称',
                dataIndex: 'name',
                key: 'name',
                width: 300,
                render: (name: string, record: MCPServerBase) => (
                    <Flex vertical gap={4}>
                        <Typography.Text ellipsis>{name}</Typography.Text>
                        <Typography.Text
                            ellipsis
                            style={{fontSize: 12, color: '#8F8F8F'}}
                        >
                            {record.description || '暂无描述'}
                        </Typography.Text>
                    </Flex>
                ),
            },
            {
                title: '协议',
                dataIndex: 'serverProtocolType',
                key: 'serverProtocolType',
                width: 120,
                filters: hasProtocolFilter ? [
                    {
                        text: 'SSE',
                        value: 'SSE',
                    },
                    {
                        text: 'STDIO',
                        value: 'STDIO',
                    },
                    {
                        text: 'Streamable HTTP',
                        value: 'Streamable_HTTP',
                    },
                ] : undefined,
                render: (value: MCPServerProtocolType) => {
                    return <MCPServerProtocolTypeTag type={value} />;
                },
            },
            {
                title: '场景',
                dataIndex: 'labels',
                key: 'labels',
                filters: labels?.map(label => ({
                    text: label.labelValue,
                    value: label.id,
                })),
                render: (labels: SpaceLabel[]) => {
                    if (labels?.length) {
                        return (
                            <TagGroup
                                labels={labels?.map(label => ({...label, label: label.labelValue}))}
                                prefix={null}
                                maxNum={3}
                            />
                        );
                    }
                },
            },
            {
                title: '操作',
                dataIndex: 'operate',
                key: 'operate',
                width: 120,
                render: (_, record) => {
                    const handleOnDetailClick = () => {
                        window.open(
                            MCPDetailLink.toUrl({mcpId: record.id}),
                            '_blank'
                        );
                    };
                    return (
                        <Button type="text" icon={<IconDetail />} onClick={handleOnDetailClick}>详情</Button>
                    );
                },
            },
        ],
        [labels, hasProtocolFilter]
    );

    return (
        <Table
            columns={columns}
            rowKey="id"
            rowSelection={{
                selectedRowKeys: selectedRowKeys,
                preserveSelectedRowKeys: true,
                onChange: (newSelectedRowKeys, selectedRows) => {
                    const addedRowKeys = difference(newSelectedRowKeys ?? [], selectedRowKeys ?? []);
                    const shouldRemovedRowKeys = addedRowKeys.map(
                        id => {
                            const row = selectedRows.find(row => row?.id === id);
                            if (row?.officialExample) {
                                return row?.originalId;
                            }
                            return id;
                        }
                    );
                    const filterdSelectedRowKeys = newSelectedRowKeys.filter(
                        (id, index) => {
                            const row = selectedRows[index];
                            if (addedRowKeys.includes(id)) {
                                return true;
                            }
                            else {
                                if (shouldRemovedRowKeys.includes(id)) {
                                    return false;
                                }
                                if (row?.officialExample && shouldRemovedRowKeys.includes(row?.originalId)) {
                                    return false;
                                }
                                return true;
                            }
                        }
                    ) as number[];
                    onSelect?.(filterdSelectedRowKeys);
                },
            }}
            pagination={{
                pageSize: 10,
                hideOnSinglePage: true,
                ...pagination,
            }}
            onChange={onChange}
            dataSource={dataSource}
            scroll={scroll}
        />
    );
}

export default memo(MCPServerTable);
