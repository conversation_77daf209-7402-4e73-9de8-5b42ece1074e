/* eslint-disable max-lines */
/* eslint-disable no-param-reassign */
/* eslint-disable max-statements-per-line */
/* eslint-disable complexity */
/* eslint-disable @typescript-eslint/init-declarations */
/* eslint-disable new-cap */
import type {JSONSchema7} from 'json-schema';
import {cloneDeep} from 'lodash';
import {MockRule} from './json-schema/mock';
import {AppJsonSchema2MockJsonSchema} from './json-schema/AppJsonSchema2JsonSchemaStandard';
import {schemaSetAdditionalPropertiesDefaultFalse} from './json-schema/schemaSetAdditionalPropertiesDefaultFalse';
import {getFirstSchemaTypeAndNullable} from './jsonUtils';
import jsf from './jsonSchemaFakerImpl';

export enum MockEngineType {
    Mockjs = 'mockjs',
    Fakerjs = 'fakerjs',
}

export const defaultOptions = {
    failOnInvalidTypes: false,
    failOnInvalidFormat: false,
    ignoreMissingRefs: true,
    enableBigInt: false,
    ignoreProperties: [] as any[],
    sortProperties: true,
    defaultMinItems: 1,
    defaultMaxItems: 1,
    refDepthMin: 1,
    refDepthMax: 1,
};

function handleMockSchemaFormat(schema: JSONSchema7, mockRule: MockRule & { format?: string }) {
    if (schema.format === mockRule.format) {
        return true;
    }
    return false;
}

function setMockInSchema(schema: JSONSchema7, mock: string) {
    const baseMockEngine = 'mockjs';
    switch (baseMockEngine) {
        case MockEngineType.Mockjs:
            // @ts-ignore
            schema.mock = {mock};
            break;
        default:
            // @ts-ignore

            break;
    }
}

const getMockByRules = (schema: JSONSchema7, mockRules: MockRule[], attributeName: string) => {
    const prioritySchemaType = getFirstSchemaTypeAndNullable(schema);

    const matchRule = mockRules.find(mockRule => {
        if (
            typeof prioritySchemaType === 'string'
      && mockRule.type.includes(prioritySchemaType as any)
        ) {
            if (schema.format && handleMockSchemaFormat(schema, mockRule)) {
                return true;
            }

            const {matchDetail: matchRuleDetail, matchType, matchCase} = mockRule;
            let matchDetailAdjust = matchRuleDetail;
            if (matchType === 'wildcard') {
                matchDetailAdjust = `^${matchDetailAdjust.replace(/\*/g, '.*').replace(/\?/g, '.')}$`;
            } else if (matchType === 'exact') {
                matchDetailAdjust = `^${matchDetailAdjust}$`;
            }
            try {
                return new RegExp(matchDetailAdjust, matchCase ? '' : 'i').test(attributeName);
            } catch (e) {
                return false;
            }
        }
        return false;
    });
    if (matchRule) {
        if (schema.format) {delete schema.format;}
        return matchRule.mock;
    }
    return undefined;
};

function schemaMockHandler(schema: JSONSchema7, mockRules: MockRule[]) {
    const refList: string[] = [];
    const existRefs: any = {};
    const schemaItemMockHandler = (
        schema: JSONSchema7 | any,
        mockRules: MockRule[],
        propertyName: string = ''
    ) => {
    // 自动有设置 mock || faker，或者 enum、minimum、maximum 则不使用项目默认 mock 规则
        if (
        // @ts-ignore
            ((schema.mock || schema.faker) && schema.type !== 'object')
      || schema.enum
      || schema.pattern
      || typeof schema.minimum === 'number'
      || typeof schema.maximum === 'number'
        ) {
            return schema;
        }
        if (schema.$ref) {
            // 处理 $ref
            if (!existRefs[schema.$ref]) {
                refList.push(schema.$ref);
                existRefs[schema.$ref] = true;
            }
            return schema;
        }
        const mock = getMockByRules(schema, mockRules, propertyName);
        if (mock) {
            setMockInSchema(schema, mock);
        } else {
            let advMode: string | undefined;
            if (Array.isArray(schema.allOf)) {
                advMode = 'allOf';
            } else if (Array.isArray(schema.anyOf)) {
                advMode = 'anyOf';
            } else if (Array.isArray(schema.oneOf)) {
                advMode = 'oneOf';
            }

            const prioritySchemaType = getFirstSchemaTypeAndNullable(schema);

            switch (prioritySchemaType) {
                case 'object':
                    if (typeof schema.properties === 'object') {
                        // jsf object 类型的时候，拓展了 JSONSchema 7 的字段，增加了随机额外属性
                        if (typeof schema.additionalProperties === 'undefined') {
                            schema.additionalProperties = false;
                        }
                        Object.keys(schema.properties).forEach(subPropertyName => {
                            const property = schema.properties[subPropertyName];
                            schema.properties[subPropertyName] = schemaItemMockHandler(
                                // @ts-ignore
                                property,
                                mockRules,
                                subPropertyName
                            );
                        });
                    }
                    if (schema.patternProperties) {
                        Object.keys(schema.patternProperties).forEach(subPropertyName => {
                            const property = schema.patternProperties[subPropertyName];
                            schema.patternProperties[subPropertyName] = schemaItemMockHandler(
                                // @ts-ignore
                                property,
                                mockRules,
                                subPropertyName
                            );
                        });
                    }
                    if (typeof schema.additionalProperties === 'object') {
                        schema.additionalProperties = schemaItemMockHandler(
                            // @ts-ignore
                            schema.additionalProperties,
                            mockRules,
                            ''
                        );
                    }
                    break;
                case 'array':
                    if (schema.items) {
                        // @ts-ignore
                        schema.items = schemaItemMockHandler(
              schema.items as JSONSchema7,
              mockRules,
              propertyName
                        );
                    }
                    break;
                default:
                    if (advMode) {
                        (schema[advMode]).forEach((item: any, i: number) => {
                            schema[advMode][i] = schemaItemMockHandler(item, mockRules);
                        });
                    }
                    break;
            }
        }

        return schema;
    };

    const newSchema = schemaItemMockHandler(cloneDeep({...schema, definitions: {}}), mockRules);
    let ref: string | undefined;

    // 处理 ref
    while ((ref = refList.pop())) {
        const refId = ref.replace(/^#\/definitions\//, '');
        if (schema.definitions && schema.definitions[refId]) {
            newSchema.definitions[refId] = schemaItemMockHandler(
                // @ts-ignore
                cloneDeep(schema.definitions[refId]),
                mockRules
            );
        }
    }
    return newSchema;
}


export default function schemaToJson(
    sourceSchema: JSONSchema7,
    options = {},
    mockRules?: MockRule[]
) {
    let schema = AppJsonSchema2MockJsonSchema(sourceSchema as any, []) as unknown as JSONSchema7;
    schema = schemaSetAdditionalPropertiesDefaultFalse(schema);
    if (Array.isArray(mockRules) && mockRules.length > 0) {
        schema = schemaMockHandler(schema, mockRules);
    }
    options = {...defaultOptions, ...options};
    jsf.option(options);
    let result;
    try {
        result = jsf.generate(schema);
    } catch (err: any) {
        result = err.message;
    }
    jsf.option(defaultOptions);
    return result;
}
