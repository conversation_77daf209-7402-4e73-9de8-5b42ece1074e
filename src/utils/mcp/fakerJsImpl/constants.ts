/* eslint-disable max-lines */
export const fakerSuggests = [
    {
        name: 'Address',
        children: [
            {name: 'Building Number', faker: 'address.buildingNumber'},
            {name: 'Cardinal Direction', faker: 'address.cardinalDirection'},
            {name: 'City', faker: 'address.city'},
            {name: 'City Name', faker: 'address.cityName'},
            {name: 'Country', faker: 'address.country'},
            {name: 'Country Code', faker: 'address.countryCode'},
            {name: 'County', faker: 'address.county'},
            {name: 'Direction', faker: 'address.direction'},
            {name: 'Latitude', faker: 'address.latitude'},
            {name: 'Longitude', faker: 'address.longitude'},
            {name: 'Nearby GPSCoordinate', faker: 'address.nearbyGPSCoordinate'},
            {name: 'Ordinal Direction', faker: 'address.ordinalDirection'},
            {name: 'Secondary Address', faker: 'address.secondaryAddress'},
            {name: 'State', faker: 'address.state'},
            {name: 'State Abbr', faker: 'address.stateAbbr'},
            {name: 'Street', faker: 'address.street'},
            {name: 'Street Address', faker: 'address.streetAddress'},
            {name: 'Street Name', faker: 'address.streetName'},
            {name: 'Time Zone', faker: 'address.timeZone'},
            {name: 'Zip Code', faker: 'address.zipCode'},
            {name: 'Zip Code By State', faker: 'address.zipCodeByState'},
        ],
    },
    {
        name: 'Animal',
        children: [
            {name: 'Bear', faker: 'animal.bear'},
            {name: 'Bird', faker: 'animal.bird'},
            {name: 'Cat', faker: 'animal.cat'},
            {name: 'Cetacean', faker: 'animal.cetacean'},
            {name: 'Cow', faker: 'animal.cow'},
            {name: 'Crocodilia', faker: 'animal.crocodilia'},
            {name: 'Dog', faker: 'animal.dog'},
            {name: 'Fish', faker: 'animal.fish'},
            {name: 'Horse', faker: 'animal.horse'},
            {name: 'Insect', faker: 'animal.insect'},
            {name: 'Lion', faker: 'animal.lion'},
            {name: 'Rabbit', faker: 'animal.rabbit'},
            {name: 'Rodent', faker: 'animal.rodent'},
            {name: 'Snake', faker: 'animal.snake'},
            {name: 'Type', faker: 'animal.Type'},
        ],
    },
    {
        name: 'Color',
        children: [
            {name: 'Cmyk', faker: 'color.cmyk'},
            {name: 'Color By CSSColor Space', faker: 'color.colorByCSSColorSpace'},
            {name: 'Css Supported Function', faker: 'color.cssSupportedFunction'},
            {name: 'Css Supported Space', faker: 'color.cssSupportedSpace'},
            {name: 'Hsl', faker: 'color.hsl'},
            {name: 'Human', faker: 'color.human'},
            {name: 'Hwb', faker: 'color.hwb'},
            {name: 'Lab', faker: 'color.lab'},
            {name: 'Lch', faker: 'color.lch'},
            {name: 'Rgb', faker: 'color.rgb'},
            {name: 'Space', faker: 'color.space'},
        ],
    },
    {
        name: 'Commerce',
        children: [
            {name: 'Color', faker: 'commerce.color'},
            {name: 'Department', faker: 'commerce.department'},
            {name: 'Price', faker: 'commerce.price'},
            {name: 'Product', faker: 'commerce.product'},
            {name: 'Product Adjective', faker: 'commerce.productAdjective'},
            {name: 'Product Description', faker: 'commerce.productDescription'},
            {name: 'Product Material', faker: 'commerce.productMaterial'},
            {name: 'Product Name', faker: 'commerce.productName'},
        ],
    },
    {
        name: 'Company',
        children: [
            {name: 'Bs', faker: 'company.bs'},
            {name: 'Bs Adjective', faker: 'company.bsAdjective'},
            {name: 'Bs Buzz', faker: 'company.bsBuzz'},
            {name: 'Bs Noun', faker: 'company.bsNoun'},
            {name: 'Catch Phrase', faker: 'company.catchPhrase'},
            {name: 'Catch Phrase Adjective', faker: 'company.catchPhraseAdjective'},
            {name: 'Catch Phrase Descriptor', faker: 'company.catchPhraseDescriptor'},
            {name: 'Catch Phrase Noun', faker: 'company.catchPhraseNoun'},
            {name: 'Company Suffix', faker: 'company.companySuffix'},
            {name: 'Suffixes', faker: 'company.suffixes'},
            {name: 'Company Name', faker: 'company.name'},
        ],
    },
    {
        name: 'Database',
        children: [
            {name: 'Collation', faker: 'database.collation'},
            {name: 'Column', faker: 'database.column'},
            {name: 'Engine', faker: 'database.engine'},
            {name: 'Mongodb Object Id', faker: 'database.mongodbObjectId'},
            {name: 'Type', faker: 'database.type'},
        ],
    },
    {
        name: 'Datatype',
        children: [
            {name: 'Array', faker: 'datatype.array'},
            {name: 'Big Int', faker: 'datatype.bigInt'},
            {name: 'Boolean', faker: 'datatype.boolean'},
            {name: 'Datetime', faker: 'datatype.datetime'},
            {name: 'Float', faker: 'datatype.float'},
            {name: 'Hexadecimal', faker: 'datatype.hexadecimal'},
            {name: 'Json', faker: 'datatype.json'},
            {name: 'Number', faker: 'datatype.number'},
            {name: 'String', faker: 'datatype.string'},
            {name: 'Uuid', faker: 'datatype.uuid'},
            {name: 'Base64', faker: 'datatype.base64'},
        ],
    },
    {
        name: 'Date',
        children: [
            {name: 'Between', faker: 'date.between'},
            {name: 'Betweens', faker: 'date.betweens'},
            {name: 'Birthdate', faker: 'date.birthdate'},
            {name: 'Future', faker: 'date.future'},
            {name: 'Month', faker: 'date.month'},
            {name: 'Past', faker: 'date.past'},
            {name: 'Recent', faker: 'date.recent'},
            {name: 'Soon', faker: 'date.soon'},
            {name: 'Weekday', faker: 'date.weekday'},
        ],
    },
    {
        name: 'Finance',
        children: [
            {name: 'Account', faker: 'finance.account'},
            {name: 'Account Name', faker: 'finance.accountName'},
            {name: 'Amount', faker: 'finance.amount'},
            {name: 'Bic', faker: 'finance.bic'},
            {name: 'Bitcoin Address', faker: 'finance.bitcoinAddress'},
            {name: 'Credit Card CVV', faker: 'finance.creditCardCVV'},
            {name: 'Credit Card Issuer', faker: 'finance.creditCardIssuer'},
            {name: 'Credit Card Number', faker: 'finance.creditCardNumber'},
            {name: 'Currency Code', faker: 'finance.currencyCode'},
            {name: 'Currency Name', faker: 'finance.currencyName'},
            {name: 'Currency Symbol', faker: 'finance.currencySymbol'},
            {name: 'Ethereum Address', faker: 'finance.ethereumAddress'},
            {name: 'Iban', faker: 'finance.iban'},
            {name: 'Litecoin Address', faker: 'finance.litecoinAddress'},
            {name: 'Mask', faker: 'finance.mask'},
            {name: 'Pin', faker: 'finance.pin'},
            {name: 'Routing Number', faker: 'finance.routingNumber'},
            {name: 'Transaction Description', faker: 'finance.transactionDescription'},
            {name: 'Transaction Type', faker: 'finance.transactionType'},
        ],
    },
    {
        name: 'Git',
        children: [
            {name: 'Branch', faker: 'git.branch'},
            {name: 'Commit Entry', faker: 'git.commitEntry'},
            {name: 'Commit Message', faker: 'git.commitMessage'},
            {name: 'Commit Sha', faker: 'git.commitSha'},
            {name: 'Short Sha', faker: 'git.shortSha'},
        ],
    },
    {
        name: 'Hacker',
        children: [
            {name: 'Abbreviation', faker: 'hacker.abbreviation'},
            {name: 'Adjective', faker: 'hacker.adjective'},
            {name: 'Ingverb', faker: 'hacker.ingverb'},
            {name: 'Noun', faker: 'hacker.noun'},
            {name: 'Phrase', faker: 'hacker.phrase'},
            {name: 'Verb', faker: 'hacker.verb'},
        ],
    },
    {
        name: 'Helpers',
        children: [
            {name: 'Array Element', faker: 'helpers.arrayElement'},
            {name: 'Array Elements', faker: 'helpers.arrayElements'},
            {name: 'Fake', faker: 'helpers.fake'},
            {name: 'Maybe', faker: 'helpers.maybe'},
            {name: 'Mustache', faker: 'helpers.mustache'},
            {name: 'Object Key', faker: 'helpers.objectKey'},
            {name: 'Object Value', faker: 'helpers.objectValue'},
            {name: 'Regexp Style String Parse', faker: 'helpers.regexpStyleStringParse'},
            {name: 'Repeat String', faker: 'helpers.repeatString'},
            {name: 'Replace Credit Card Symbols', faker: 'helpers.replaceCreditCardSymbols'},
            {name: 'Replace Symbol With Number', faker: 'helpers.replaceSymbolWithNumber'},
            {name: 'Replace Symbols', faker: 'helpers.replaceSymbols'},
            {name: 'Shuffle', faker: 'helpers.shuffle'},
            {name: 'Slugify', faker: 'helpers.slugify'},
            {name: 'Unique Array', faker: 'helpers.uniqueArray'},
        ],
    },
    {
        name: 'Image',
        children: [
            {name: 'Abstract', faker: 'image.abstract'},
            {name: 'Animals', faker: 'image.animals'},
            {name: 'Avatar', faker: 'image.avatar'},
            {name: 'Business', faker: 'image.business'},
            {name: 'Cats', faker: 'image.cats'},
            {name: 'City', faker: 'image.city'},
            {name: 'Data Uri', faker: 'image.dataUri'},
            {name: 'Fashion', faker: 'image.fashion'},
            {name: 'Food', faker: 'image.food'},
            {name: 'Image', faker: 'image.image'},
            {name: 'Image Url', faker: 'image.imageUrl'},
            {name: 'Nature', faker: 'image.nature'},
            {name: 'Nightlife', faker: 'image.nightlife'},
            {name: 'People', faker: 'image.people'},
            {name: 'Sports', faker: 'image.sports'},
            {name: 'Technics', faker: 'image.technics'},
            {name: 'Transport', faker: 'image.transport'},
        ],
    },
    {
        name: 'Internet',
        children: [
            {name: 'Avatar', faker: 'internet.avatar'},
            {name: 'Color', faker: 'internet.color'},
            {name: 'Domain Name', faker: 'internet.domainName'},
            {name: 'Domain Suffix', faker: 'internet.domainSuffix'},
            {name: 'Domain Word', faker: 'internet.domainWord'},
            {name: 'Email', faker: 'internet.email'},
            {name: 'Emoji', faker: 'internet.emoji'},
            {name: 'Example Email', faker: 'internet.exampleEmail'},
            {name: 'Http Method', faker: 'internet.httpMethod'},
            {name: 'Http Status Code', faker: 'internet.httpStatusCode'},
            {name: 'Ip', faker: 'internet.ip'},
            {name: 'Ipv4', faker: 'internet.ipv4'},
            {name: 'Ipv6', faker: 'internet.ipv6'},
            {name: 'Mac', faker: 'internet.mac'},
            {name: 'Password', faker: 'internet.password'},
            {name: 'Port', faker: 'internet.port'},
            {name: 'Protocol', faker: 'internet.protocol'},
            {name: 'Url', faker: 'internet.url'},
            {name: 'User Agent', faker: 'internet.userAgent'},
            {name: 'User Name', faker: 'internet.userName'},
        ],
    },
    {
        name: 'Lorem',
        children: [
            {name: 'Lines', faker: 'lorem.lines'},
            {name: 'Paragraph', faker: 'lorem.paragraph'},
            {name: 'Paragraphs', faker: 'lorem.paragraphs'},
            {name: 'Sentence', faker: 'lorem.sentence'},
            {name: 'Sentences', faker: 'lorem.sentences'},
            {name: 'Slug', faker: 'lorem.slug'},
            {name: 'Text', faker: 'lorem.text'},
            {name: 'Word', faker: 'lorem.word'},
            {name: 'Words', faker: 'lorem.words'},
        ],
    },
    {
        name: 'Mersenne',
        children: [
            {name: 'Rand', faker: 'mersenne.rand'},
            {name: 'Seed', faker: 'mersenne.seed'},
            {name: 'Seed_array', faker: 'mersenne.seed_array'},
        ],
    },
    {
        name: 'Music',
        children: [
            {name: 'Genre', faker: 'music.genre'},
            {name: 'Song Name', faker: 'music.songName'},
        ],
    },
    {
        name: 'Name',
        children: [
            {name: 'First Name', faker: 'name.firstName'},
            {name: 'Full Name', faker: 'name.fullName'},
            {name: 'Gender', faker: 'name.gender'},
            {name: 'Job Area', faker: 'name.jobArea'},
            {name: 'Job Descriptor', faker: 'name.jobDescriptor'},
            {name: 'Job Title', faker: 'name.jobTitle'},
            {name: 'Job Type', faker: 'name.jobType'},
            {name: 'Last Name', faker: 'name.lastName'},
            {name: 'Middle Name', faker: 'name.middleName'},
            {name: 'Prefix', faker: 'name.prefix'},
            {name: 'Suffix', faker: 'name.suffix'},
        ],
    },
    {
        name: 'Phone',
        children: [
            {name: 'Imei', faker: 'phone.imei'},
            {name: 'Number', faker: 'phone.number'},
        ],
    },
    {
        name: 'Random',
        children: [
            {name: 'Alpha', faker: 'random.alpha'},
            {name: 'Alpha Numeric', faker: 'random.alphaNumeric'},
            {name: 'Locale', faker: 'random.locale'},
            {name: 'Numeric', faker: 'random.numeric'},
            {name: 'Word', faker: 'random.word'},
            {name: 'Words', faker: 'random.words'},
        ],
    },
    {
        name: 'Science',
        children: [
            {name: 'Chemical Element', faker: 'science.chemicalElement'},
            {name: 'Unit', faker: 'science.unit'},
        ],
    },
    {
        name: 'System',
        children: [
            {name: 'Common File Ext', faker: 'system.commonFileExt'},
            {name: 'Common File Name', faker: 'system.commonFileName'},
            {name: 'Common File Type', faker: 'system.commonFileType'},
            {name: 'Directory Path', faker: 'system.directoryPath'},
            {name: 'File Ext', faker: 'system.fileExt'},
            {name: 'File Name', faker: 'system.fileName'},
            {name: 'File Path', faker: 'system.filePath'},
            {name: 'File Type', faker: 'system.fileType'},
            {name: 'Mime Type', faker: 'system.mimeType'},
            {name: 'Network Interface', faker: 'system.networkInterface'},
            {name: 'Semver', faker: 'system.semver'},
        ],
    },
    // {
    //   name: 'Unique',
    //   children: [{ name: 'Unique', faker: 'unique' }],
    // },
    {
        name: 'Vehicle',
        children: [
            {name: 'Bicycle', faker: 'vehicle.bicycle'},
            {name: 'Color', faker: 'vehicle.color'},
            {name: 'Fuel', faker: 'vehicle.fuel'},
            {name: 'Manufacturer', faker: 'vehicle.manufacturer'},
            {name: 'Model', faker: 'vehicle.model'},
            {name: 'Type', faker: 'vehicle.type'},
            {name: 'Vin', faker: 'vehicle.vin'},
            {name: 'Vrm', faker: 'vehicle.Vrm'},
        ],
    },
    {
        name: 'Word',
        children: [
            {name: 'Adjective', faker: 'word.adjective'},
            {name: 'Adverb', faker: 'word.adverb'},
            {name: 'Conjunction', faker: 'word.conjunction'},
            {name: 'Interjection', faker: 'word.interjection'},
            {name: 'Noun', faker: 'word.noun'},
            {name: 'Preposition', faker: 'word.preposition'},
            {name: 'Verb', faker: 'word.verb'},
        ],
    },
];
