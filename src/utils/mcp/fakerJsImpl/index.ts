/* eslint-disable max-statements-per-line */
import {flatMap} from 'lodash';
import {fakerSuggests} from './constants';

const {faker} = require('@faker-js/faker');

// 拓展
faker.datatype.base64 = () => {
    let result = 'SGVsbG8sIHdvcmxk';
    try {
        result = btoa(faker.datatype.string());
    } catch (error) {
    //
    }
    return result;
};

const DEFAULT_FAKER_VALUE = '';

export function setFakerJsLocale(local: string) {
    // eslint-disable-next-line no-param-reassign
    if (!faker.locales[local]) {local = 'en';}
    faker.setLocale(local);
}

const fakerMethodList = fakerSuggests.map(item => item.name.toLocaleLowerCase());
const fakerSubMethodList = flatMap(
    fakerSuggests.map(item => item.children),
    item => item
).map(item => item.faker.split('.')[1]);

function verifyFakerExpression(expression: string) {
    const [method, subMethod] = expression.split('.');
    if (
        !fakerMethodList.includes(method)
    || !subMethod
    || !fakerSubMethodList.includes(subMethod.split('(')[0])
    ) {
        throw Error(`invalid expression: ${expression}`);
    }
}

function touchStr(str: string) {
    return !str.includes('(') ? `${str}()` : str;
}

function changeDateStringValueFormat(value: Date) {
    // eslint-disable-next-line no-param-reassign
    value.toString = Date.prototype.toISOString;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function fakerJsImpl(value: unknown, _schema: unknown) {
    try {
        if (!value || typeof value !== 'string') {return DEFAULT_FAKER_VALUE;}

        verifyFakerExpression(value);

        const expression = touchStr(value);

        // eslint-disable-next-line no-eval
        const result = eval(`faker.${expression}`);

        if (result instanceof Date) {
            changeDateStringValueFormat(result);
        }

        return result;
    } catch (error) {
        return value;
    }
}
