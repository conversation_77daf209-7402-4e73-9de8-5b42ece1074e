/* eslint-disable max-lines */
/* eslint-disable max-statements-per-line */
/* eslint-disable max-statements */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable new-cap */
/* eslint-disable complexity */
import {JSONSchema} from './type';
import {
    defaultSchema,
    getAllExpendKeys,
    getMainType,
    getRef,
    isIncludeCycleDeps,
    isNullable,
    isNullableRef,
} from './utils';

enum SchemaParserError {
    NotFoundInRefStack = 'NotFoundInRefStack',
    CycleRef = 'CycleRefBreak',
    Unknown = 'Unknown Error',
}

function GetRefData(sourceData: JSONSchema, dataSchemaDefinitions: JSONSchema['definitions'] = {}) {
    // 因为 nullable ref 的实现，所以这里需要进行路径压缩
    // lazyData 操作修改自身属性的
    // childNeedLazyData 子级所需要的 JsonSchema lazyData,
    // 直接在这里进行循环引用的判断。
    let nullable = false;
    const refStack: string[] = [];
    let dataType = getMainType(sourceData);
    if (dataType !== 'ref') {
        return {
            res: {
                RefSourceObject: null,
                RefId: null,
                nullable: isNullable(sourceData),
                datatype: dataType,
                refStack,
            },
            error: null,
        };
    }
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const {RefSourceObject, RefId, RefTitle} = getRef(sourceData, dataSchemaDefinitions);

    if (RefSourceObject === null || RefId === null || RefTitle === null) {
        // throw 引用类型栈中找不到变量。
        return {res: null, error: SchemaParserError.NotFoundInRefStack};
    }
    if (nullable || isNullableRef(sourceData)) {
        nullable = true;
    }

    refStack.push(RefId);
    let cursor = RefSourceObject;
    while (cursor !== null) {
        dataType = getMainType(cursor);
        if (dataType === 'ref') {
        // eslint-disable-next-line @typescript-eslint/no-shadow
            const {RefSourceObject, RefId: XRefId} = getRef(cursor, dataSchemaDefinitions);
            if (RefSourceObject === null || XRefId === null) {
                // throw 引用类型栈中找不到变量。
                return {res: null, error: SchemaParserError.NotFoundInRefStack};
            }
            if (refStack.includes(XRefId)) {
                return {res: null, error: SchemaParserError.CycleRef};
            }
            if (nullable || isNullableRef(cursor)) {
                nullable = true;
            }
            cursor = RefSourceObject;
            refStack.push(XRefId);
        } else if (dataType === 'object') {
            return {
                res: {
                    RefSourceObject: cursor,
                    nullable: nullable || isNullable(cursor),
                    RefId,
                    RefTitle,
                    datatype: 'object',
                    refStack,
                },
                error: null,
            };
        } else {
            return {
                res: {
                    RefSourceObject: cursor,
                    nullable: nullable || isNullable(cursor),
                    RefId,
                    RefTitle,
                    datatype: dataType,
                    refStack,
                },
                error: null,
            };
        }
    }
    return {
        res: {
            ...getRef(sourceData, dataSchemaDefinitions),
            datatype: dataType,
            nullable: isNullable(sourceData),
            refStack,
        },
        error: SchemaParserError.Unknown,
    };
}

export function AppJsonSchema2MockJsonSchema(RootSchema: JSONSchema, RootSchemaId: string[] = []) {
    const handledIds = new Set<string>();
    const dataSchemaDefinitions = {...RootSchema.definitions};
    const {expendRefsKey, expendOrdersKey, expendIgnorePropertiesKey, expendOverridesKey} =
    getAllExpendKeys();

    function RefSchemaCore({
        draftSchema,
        schemaId,
        XOverrides = {},
        XOverridesRequired,
        ValidFieldSet = new Set(),
    }: {
    draftSchema: JSONSchema;
    schemaId: string[];
    XOverridesRequired?: string[];
    XOverrides?: JSONSchema['x-app-overrides'];
    ValidFieldSet?: Set<string>;
  }): {
    RequiredProps: Set<string>;
    properties: Record<string, JSONSchema>;
    XAppIgnoreProperties: string[];
    ObjectOrder: string[];
  } {
        const mainType = getMainType(draftSchema);
        let RefSourceObject: JSONSchema | null | undefined = null;

        if (mainType === 'ref') {
            const {res, error} = GetRefData(draftSchema, dataSchemaDefinitions);

            if (error) {
                // ...
                return {
                    RequiredProps: new Set(),
                    properties: {},
                    XAppIgnoreProperties: [],
                    ObjectOrder: [],
                };
            }
            RefSourceObject = res?.RefSourceObject;
        } else if (mainType === 'object') {
            RefSourceObject = draftSchema;
        }

        if (!RefSourceObject) {
            return {
                RequiredProps: new Set(),
                properties: {},
                XAppIgnoreProperties: [],
                ObjectOrder: [],
            };
        }

        const {RequiredProps, properties, ObjectOrder} = ObjectSchemaCore({
            draftSchema: RefSourceObject,
            schemaId,
            ValidFieldSet,
        });

        const XOverridesKeys = Object.keys(XOverrides).filter(v => v in properties);
        // const AddFieldList = XOverridesOrder.filter((v) => !HideFieldList.includes(v));
        const ValidXOverridesRequired = new Set(
            XOverridesRequired?.filter(v => XOverridesKeys.includes(v))
        );

        XOverridesKeys.forEach(v => {
            if (XOverrides[v] === null) {
                RequiredProps.delete(v);
                ValidFieldSet.delete(v);
                delete properties[v];
            } else {
                // 非引用字段，优先级更高。
                // 原地替换
                ValidFieldSet.add(v);
                properties[v] = SchemaCore({
                    draftSchema: XOverrides[v],
                    schemaId,
                });
                if (RequiredProps.has(v) && !ValidXOverridesRequired.has(v)) {
                    RequiredProps.delete(v);
                } else if (!RequiredProps.has(v) && ValidXOverridesRequired.has(v)) {
                    RequiredProps.add(v);
                }
            }
        });
        // AddFieldList.forEach((v) => {
        //   RequiredProps.delete(v);
        //   if (XOverrides[v]) {
        //     // 非引用字段，优先级更高。
        //     ValidFieldSet.add(v);
        //     properties[v] = SchemaCore({
        //       draftSchema: XOverrides[v]!,
        //       schemaId,
        //     });
        //     ObjectOrder.push(v);
        //   }
        // });

        return {
            RequiredProps,
            properties,
            XAppIgnoreProperties: Object.keys(properties),
            ObjectOrder,
        };
    }

    function ObjectSchemaCore({
        draftSchema,
        schemaId,
        ValidFieldSet = new Set(),
    }: {
    draftSchema: JSONSchema;
    schemaId: string[];
    ValidFieldSet?: Set<string>;
  }) {
    // 同名字段覆盖/展示逻辑：
    // 1. “非引用字段”的优先级高于“引用字段”
    // 2. “同为引用”或“同为非引用”的字段之间的优先级：“排序在前的字段”优先级高于“排序在后的字段”
    // 3. 无效字段的“字段名”显示删除线，tooltip提示“该字段无效，存在同名字段”
        const RequiredProps: Set<string> = new Set(draftSchema.required);
        const OptionalProps: Set<string> = new Set();
        let properties: Record<string, JSONSchema> = {};
        let XAppIgnoreProperties: string[] = [];
        const ObjectPropertiesList = Object.keys(draftSchema?.properties || {});
        const ObjectOrder = getActiveXAppOrder(draftSchema);

        ObjectOrder.forEach(objKey => {
            if (ObjectPropertiesList.includes(objKey)) {
                ValidFieldSet.add(objKey);
            }
        });
        ObjectOrder.forEach(objKey => {
            const XRefsData = draftSchema[expendRefsKey]?.[objKey];
            if (XRefsData) {
                const {res} = GetRefData(XRefsData, dataSchemaDefinitions);
                if (!res) {return;}
                const {RefSourceObject: SourceRef, RefId} = res;
                if (!SourceRef) {return;}
                const mainType = getMainType(SourceRef);
                if (!(mainType === 'object' || mainType === 'ref')) {
                    return;
                }
                const RefSourceObject = JSON.parse(JSON.stringify(SourceRef));

                const IncludeCycleDeps = isIncludeCycleDeps(schemaId, RefId);
                if (IncludeCycleDeps) {return;}

                const RefSnap = RefSchemaCore({
                    draftSchema: RefSourceObject,
                    schemaId: [...schemaId, RefId!],
                    XOverrides: XRefsData[expendOverridesKey],
                    XOverridesRequired: XRefsData.required,
                    // ValidFieldSet,
                });
                const activeRefProps = Object.keys(RefSnap.properties)
                    .filter(k => {
                        return !ValidFieldSet.has(k);
                    })
                    .reduce((activeProps, propsKey) => {
                        if (RefSnap.properties[propsKey] && !ObjectPropertiesList.includes(propsKey)) {
                            // eslint-disable-next-line no-param-reassign
                            activeProps[propsKey] = RefSnap.properties[propsKey];
                            ValidFieldSet.add(propsKey);

                            if (RefSnap.RequiredProps.has(propsKey)) {
                                RequiredProps.add(propsKey);
                                OptionalProps.delete(propsKey);
                            } else {
                                OptionalProps.add(propsKey);
                                RequiredProps.delete(propsKey);
                            }
                        }
                        return activeProps;
                    }, {} as typeof RefSnap.properties);
                properties = {
                    ...properties,
                    // TODO fix 优先级问题
                    ...activeRefProps,
                };
            }
            const subSchemaData = draftSchema.properties?.[objKey];
            if (subSchemaData) {
                if (subSchemaData['x-tmp-pending-properties']) {return;}
                const required = !!draftSchema.required?.includes(objKey);
                properties[objKey] = SchemaCore({
                    draftSchema: subSchemaData,
                    schemaId,
                });
                if (required) {
                    RequiredProps.add(objKey);
                    OptionalProps.delete(objKey);
                } else {
                    RequiredProps.delete(objKey);
                    OptionalProps.add(objKey);
                }
            }
        });

        XAppIgnoreProperties = Object.keys(properties).filter(p => {
            return !ObjectPropertiesList.includes(p);
        });

        const isNullableObject = isNullable(draftSchema);

        const newSchema: JSONSchema = {
            ...draftSchema,
            type: isNullableObject ? ['object', 'null'] : 'object',
            properties,
            required: RequiredProps.size === 0 ? undefined : Array.from(RequiredProps),
            [expendIgnorePropertiesKey]: Array.from(XAppIgnoreProperties),
            [expendOrdersKey]: ObjectOrder,
        };
        // newSchema.properties = reOrderProps(newSchema);
        // eslint-disable-next-line no-param-reassign
        delete newSchema.$ref;
        if (RequiredProps.size === 0) {
            // eslint-disable-next-line no-param-reassign
            delete newSchema.required;
        }

        return {
            schema: newSchema,
            RequiredProps,
            OptionalProps,
            properties,
            XAppIgnoreProperties,
            ObjectOrder,
        };
    }

    function SchemaCore({
        draftSchema,
        schemaId,
    }: {
    draftSchema: JSONSchema;
    schemaId: string[];
  }): JSONSchema {
        if ((draftSchema as any).nullable === true) {
            if (draftSchema.type) {
                const type = (
                    Array.isArray(draftSchema.type) ? draftSchema.type : [draftSchema.type]
                ).filter(v => v !== 'null');
                // eslint-disable-next-line no-param-reassign
                draftSchema.type = [...Array.from(new Set(type)), 'null'];
            } else if (draftSchema.$ref) {
                // eslint-disable-next-line no-param-reassign
                delete (draftSchema as any).nullable;
                const collectSubSchema = {...draftSchema};
                // emptyAll
                Object.keys(draftSchema).forEach(k => {
                    // eslint-disable-next-line no-param-reassign
                    delete draftSchema[k];
                });
                // eslint-disable-next-line no-param-reassign
                draftSchema.anyOf = [
                    collectSubSchema,
                    {
                        type: 'null',
                    },
                ];
            } else {
                // eslint-disable-next-line no-param-reassign
                draftSchema.type = 'null';
            }
        }
        if ((draftSchema as any).nullable !== undefined) {
            // eslint-disable-next-line no-param-reassign
            delete (draftSchema as any).nullable;
        }

        const mainType = getMainType(draftSchema);
        if (
            typeof draftSchema === 'object'
      && ('allOf' in draftSchema || 'anyOf' in draftSchema || 'oneOf' in draftSchema)
        ) {
            const operator = (() => {
                if ('allOf' in draftSchema) {return 'allOf';}
                if ('anyOf' in draftSchema) {return 'anyOf';}
                if ('oneOf' in draftSchema) {return 'oneOf';}
                return '';
            })();
            if (operator) {
                return {
                    ...draftSchema,
                    [operator]:
            draftSchema[operator]
                ?.filter(item => {
                    return typeof item === 'object' && !!item && !Array.isArray(item);
                })
                .map(item => {
                    return SchemaCore({
                        draftSchema: item,
                        schemaId,
                    });
                }) || [],
                };
            }
            return draftSchema;
        }
        if (!['ref', 'oneOf', 'anyOf', 'allOf', 'object', 'array'].includes(mainType)) {
            // string, number, integer, boolean, null, any, not 原样输出
            return draftSchema;
        }
        if (mainType === 'array') {
            return {
                ...draftSchema,
                items: SchemaCore({
                    draftSchema: draftSchema.items || (defaultSchema.string() as any),
                    schemaId,
                }),
            };
        }
        if (mainType === 'ref') {
            const {res} = GetRefData(draftSchema, dataSchemaDefinitions);
            const {RefSourceObject, datatype, refStack} = res || {};
            if (res && RefSourceObject && refStack) {
                if (datatype === 'object' && !RefSourceObject[expendIgnorePropertiesKey]) {
                    const RefId = refStack[refStack.length - 1];

                    if (!handledIds.has(RefId)) {
                        handledIds.add(RefId);
                        dataSchemaDefinitions[RefId] = ObjectSchemaCore({
                            draftSchema: JSON.parse(JSON.stringify(RefSourceObject)),
                            schemaId: [...schemaId, ...refStack],
                        }).schema;
                    }
                    const omitTypeSchema = {
                        ...draftSchema,
                    };
                    delete omitTypeSchema.type;
                    return {
                        ...omitTypeSchema,
                        $ref: `#/definitions/${RefId}`,
                        definitions: draftSchema.definitions,
                    };
                }

                if (datatype === 'array') {
                    const RefId = refStack[refStack.length - 1];

                    if (!handledIds.has(RefId)) {
                        handledIds.add(RefId);
                        dataSchemaDefinitions[RefId].items = SchemaCore({
                            draftSchema: dataSchemaDefinitions[RefId].items || (defaultSchema.string() as any),
                            schemaId: [...schemaId, ...refStack],
                        });
                    }
                }
            }
            return draftSchema;
        }
        if (mainType === 'object') {
            if (draftSchema[expendIgnorePropertiesKey]) {
                return draftSchema;
            }

            return ObjectSchemaCore({
                draftSchema,
                schemaId,
            }).schema;
        }
        return draftSchema;
    }

    return RootSchema.definitions
        ? {
            ...SchemaCore({
                draftSchema: RootSchema,
                schemaId: RootSchemaId,
            }),
            definitions: dataSchemaDefinitions,
        }
        : SchemaCore({
            draftSchema: RootSchema,
            schemaId: RootSchemaId,
        });
}

export function getActiveXAppOrder(schema: JSONSchema): string[] {
    const {expendRefsKey, expendOrdersKey} = getAllExpendKeys();
    const allPropertiesDefaultOrder = Object.keys(schema.properties || {}).concat(
        Object.keys(schema[expendRefsKey] || {})
    );
    const xAppOrders = schema[expendOrdersKey];
    if (!Array.isArray(xAppOrders)) {
        return allPropertiesDefaultOrder;
    }
    // 排除已删除节点
    const pickExistFieldOrder: string[] = Array.from(
        new Set(xAppOrders.filter(v => allPropertiesDefaultOrder.includes(v)))
    );
    // 未包含在 order 的节点
    const restOrder: string[] = Array.from(
        new Set(allPropertiesDefaultOrder.filter(v => !pickExistFieldOrder.includes(v)))
    );
    if (pickExistFieldOrder.length === xAppOrders.length && restOrder.length === 0) {
        return xAppOrders;
    }
    return Array.from(new Set([...pickExistFieldOrder, ...restOrder]));
}

export function reOrderProps(schema: JSONSchema): Record<string, JSONSchema> | undefined {
    return schema.properties;
}
