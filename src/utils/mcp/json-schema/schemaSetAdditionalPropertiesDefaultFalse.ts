/* eslint-disable complexity */
/* eslint-disable max-statements-per-line */
import type {JSONSchema7} from 'json-schema';
import {cloneDeep} from 'lodash';
import traverse from './traverse';

export function schemaSetAdditionalPropertiesDefaultFalse(sourceSchema: JSONSchema7) {
    const newSchemaCopy = cloneDeep(sourceSchema);

    traverse(newSchemaCopy, schema => {
        if (!schema || typeof schema !== 'object') {
            return;
        }

        const additionalPropertiesUseFul =
      typeof schema.additionalProperties === 'object'
      && Object.keys(schema.additionalProperties).length > 0;
        if (additionalPropertiesUseFul) {return;}

        const patternPropertiesUseFul =
      typeof schema.patternProperties === 'object'
      && Object.keys(schema.patternProperties).length > 0;

        const additionalPropertiesIsEmptyObject =
      typeof schema.additionalProperties === 'object'
      && Object.keys(schema.additionalProperties).length === 0;

        const propertiesIsEmptyObject =
      typeof schema.properties === 'object' && Object.keys(schema.properties).length === 0;

        if (patternPropertiesUseFul) {
            if (additionalPropertiesIsEmptyObject) {
                // eslint-disable-next-line no-param-reassign
                delete schema.additionalProperties;
            }

            if (propertiesIsEmptyObject) {
                // eslint-disable-next-line no-param-reassign
                delete schema.properties;
            }
        } else {
            // eslint-disable-next-line no-param-reassign
            schema.additionalProperties = false;
        }
    });

    return newSchemaCopy;
}
