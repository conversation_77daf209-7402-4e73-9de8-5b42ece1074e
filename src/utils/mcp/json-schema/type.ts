/* eslint-disable @typescript-eslint/no-empty-interface */
import type {JSONSchema7} from 'json-schema';

export interface JSONSchema extends JSONSchema7 {
  name?: string;
  required?: string[];
  /**
   * May only be defined when "items" is defined, and is a tuple of JSONSchemas.
   *
   * This provides a definition for additional items in an array instance when tuple definitions of
   * the items is provided. This can be false to indicate additional items in the array are not
   * allowed, or it can be a schema that defines the schema of the additional items.
   *
   * @see https://tools.ietf.org/html/draft-zyp-json-schema-03#section-5.6
   */
  additionalItems?: boolean | JSONSchema;
  /**
   * This attribute defines the allowed items in an instance array, and MUST be a schema or an array
   * of schemas. The default value is an empty schema which allows any value for items in the instance array.
   *
   * When this attribute value is a schema and the instance value is an array, then all the items in
   * the array MUST be valid according to the schema.
   *
   * When this attribute value is an array of schemas and the instance value is an array, each
   * position in the instance array MUST conform to the schema in the corresponding position for
   * this array. This called tuple typing. When tuple typing is used, additional items are allowed,
   * disallowed, or constrained by the "additionalItems" (Section 5.6) attribute using the same
   * rules as "additionalProperties" (Section 5.4) for objects.
   *
   * @see https://tools.ietf.org/html/draft-zyp-json-schema-03#section-5.5
   */
  items?: JSONSchema | JSONSchema[];
  /**
   * This attribute defines a schema for all properties that are not explicitly defined in an object
   * type definition. If specified, the value MUST be a schema or a boolean. If false is provided,
   * no additional properties are allowed beyond the properties defined in the schema. The default
   * value is an empty schema which allows any value for additional properties.
   *
   * @see https://tools.ietf.org/html/draft-zyp-json-schema-03#section-5.4
   */
  additionalProperties?: boolean | JSONSchema;
  definitions?: Record<string, JSONSchema>;
  /**
   * This attribute is an object with property definitions that define the valid values of instance
   * object property values. When the instance value is an object, the property values of the
   * instance object MUST conform to the property definitions in this object. In this object, each
   * property definition's value MUST be a schema, and the property's name MUST be the name of the
   * instance property that it defines. The instance property value MUST be valid according to the
   * schema from the property definition. Properties are considered unordered, the order of the
   * instance properties MAY be in any order.
   *
   * @see https://tools.ietf.org/html/draft-zyp-json-schema-03#section-5.2
   */
  properties?: Record<string, JSONSchema>;
  /**
   * This attribute is an object that defines the schema for a set of property names of an object
   * instance. The name of each property of this attribute's object is a regular expression pattern
   * in the ECMA 262/Perl 5 format, while the value is a schema. If the pattern matches the name of
   * a property on the instance object, the value of the instance's property MUST be valid against
   * the pattern name's schema value.
   *
   * @see https://tools.ietf.org/html/draft-zyp-json-schema-03#section-5.3
   */
  patternProperties?: Record<string, JSONSchema>;
  dependencies?: {
    [k: string]: JSONSchema | string[];
  };
  allOf?: JSONSchema[];
  anyOf?: JSONSchema[];
  oneOf?: JSONSchema[];
  not?: JSONSchema;
  // ---- 以下是扩展的属性 ----
  /**
   * 在当前 JSONSchema 中插入 refs，必须与当前 type 一致
   *
   * - Type object 时才生效，refs schema type 也必须是 object
   *
   * - 效果就是将 refs object properties 合并到 原来的 object
   *
   * - 属性Key uuid4 保证唯一性
   */
  'x-app-refs'?: Record<string, JSONSchema>;
  /**
   * - Object overrides 实现复写 merge 当前 properties
   *
   * - Null 表示隐藏该字段
   *
   * - 非 null 表示覆写该字段（不许增加）
   */
  'x-app-overrides'?: Record<string, null | JSONSchema>;
  /** 字段排序，包括 x-app-refs 中引入的 $ref 块的排序 */
  'x-app-orders'?: readonly string[];
  /** 声明忽略对象：x-app-ignore-properties */
  'x-app-ignore-properties'?: string[];
  'x-app-$ref'?: JSONSchema7['$ref'];
  [extension: string]: any;
}

export type IDataSchemaDefinitions = Record<string, JSONSchema>;

// TODO 抽象出 JSONSchema 的 Node
export interface JSONSchemaNode {}
