/* eslint-disable max-lines */
/* eslint-disable max-statements-per-line */
/* eslint-disable @typescript-eslint/prefer-for-of */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable max-depth */
/* eslint-disable complexity */
import {isEqual} from 'lodash';
import type {JSONSchema} from './type';

export const defaultSchema = {
    string: () =>
        ({
            type: 'string',
        } as JSONSchema),
    integer: () =>
        ({
            type: 'integer',
        } as JSONSchema),
    boolean: () =>
        ({
            type: 'boolean',
        } as JSONSchema),
    array: () =>
        ({
            type: 'array',
            items: {
                type: 'string',
            },
        } as JSONSchema),
    object: () =>
        ({
            type: 'object',
            properties: {},
        } as JSONSchema),
    number: () =>
        ({
            type: 'number',
        } as JSONSchema),
    null: () =>
        ({
            type: 'null',
        } as JSONSchema),
    any: () =>
        ({
            type: 'any',
        } as unknown as JSONSchema),
    oneOf: () =>
        ({
            oneOf: [],
        } as JSONSchema),
    anyOf: () =>
        ({
            anyOf: [],
        } as JSONSchema),
    allOf: () =>
        ({
            allOf: [],
        } as JSONSchema),
} as const;

export const isIncludeCycleDeps = (
    schemaId: string[],
    RefId: string | null | undefined | string[]
) => {
    if (RefId === null) {return false;}
    if (typeof RefId === 'string') {return schemaId.includes(RefId);}

    for (let index = 0; index < RefId.length; index += 1) {
        const id = RefId[index];
        if (isIncludeCycleDeps(schemaId, id)) {
            return true;
        }
    }
    return false;
};

export function getAllExpendKeys() {
    const appNameLower = 'iapi';
    return {
        expendOrdersKey: `x-${appNameLower}-orders`,
        expendOverridesKey: `x-${appNameLower}-overrides`,
        expendIgnorePropertiesKey: `x-${appNameLower}-ignore-properties`,
        expendRefsKey: `x-${appNameLower}-refs`,
        expend$RefsKey: `x-${appNameLower}-$ref`,
    };
}

export function isCompactNullableRef(schema: JSONSchema): boolean {
    const {oneOf} = schema;
    return (
        Array.isArray(oneOf)
    && oneOf.length === 2
    && typeof oneOf[0] === 'object'
    && typeof oneOf[0].$ref === 'string'
    && typeof oneOf[1] === 'object'
    && oneOf[1].type === 'null'
    && Object.keys(oneOf[1]).length === 1 // 为了减少影响。
    );
}

export function isNullableRef(schema: JSONSchema): boolean {
    const {anyOf} = schema;
    return (
        isCompactNullableRef(schema)
    || (Array.isArray(anyOf)
      && anyOf.length === 2
      && typeof anyOf[0] === 'object'
      && typeof anyOf[0].$ref === 'string'
      && typeof anyOf[1] === 'object'
      && anyOf[1].type === 'null')
    );
}

export function isNullable(schema: JSONSchema): boolean {
    const {type} = schema;
    return type === 'null' || (Array.isArray(type) && type.includes('null')) || isNullableRef(schema);
}

export function getRef(data: JSONSchema, dataSchemaDefinitions?: JSONSchema['definitions']) {
    if (isRef(data)) {
        if (isNullableRef(data)) {
            const RefSchema = (data.anyOf ?? data.oneOf)?.[0] ?? {};
            const RefId = RefSchema.$ref?.replace('#/definitions/', '');
            if (RefId) {
                const RefSourceObject = dataSchemaDefinitions?.[RefId];
                if (RefSourceObject) {
                    return {
                        isRef: true,
                        RefSourceObject,
                        RefId,
                        RefTitle: RefSourceObject.title,
                        accessor: (data: JSONSchema) => (data.anyOf ?? data.oneOf)[0],
                        isNullableRef: true,
                    } as const;
                }
            }
        } else {
            const RefId = data.$ref?.replace('#/definitions/', '');
            if (RefId) {
                const RefSourceObject = dataSchemaDefinitions?.[RefId];
                if (RefSourceObject) {
                    return {
                        isRef: true,
                        RefSourceObject,
                        RefId,
                        RefTitle: RefSourceObject.title,
                        accessor: (data: JSONSchema) => data,
                        isNullableRef: false,
                    } as const;
                }
            }
        }
    }
    return {
        isRef: false,
        accessor: null,
        RefSourceObject: null,
        RefId: null,
        RefTitle: null,
        isNullableRef: undefined,
    } as any;
}

export const schemaTypeList = ['string', 'integer', 'boolean', 'array', 'object', 'number', 'null'];

export function getMainType(schema: JSONSchema) {
    if (typeof schema === 'boolean' || schema === undefined) {
        return 'other';
    }
    const {type, $ref, oneOf, anyOf, allOf, not, properties} = schema;
    if ($ref || isNullableRef(schema)) {
        return 'ref';
    }
    if (typeof type === 'string') {
        return type;
    }
    if (type === undefined && typeof properties === 'object') {
        return 'object';
    }
    if (Array.isArray(type) && isEqual(type, schemaTypeList)) {
        return 'any';
    }
    if (Array.isArray(type) && type.length === 2 && type[1] === 'null' && type[0] !== 'null') {
        return type[0];
    }
    if (oneOf) {
        return 'oneOf';
    }
    if (anyOf) {
        return 'anyOf';
    }
    if (allOf) {
        return 'allOf';
    }
    if (not) {
        return 'not';
    }
    return 'other';
}

export function isRef(data: JSONSchema) {
    return getMainType(data) === 'ref';
}

export function isObject(data: JSONSchema) {
    return getMainType(data) === 'object';
}
