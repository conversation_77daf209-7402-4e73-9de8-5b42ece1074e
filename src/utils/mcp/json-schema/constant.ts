import {MockRule} from './mock';

/* eslint-disable max-lines */
const mockRules = [
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'avatar|icon',
        'matchCase': false,
        'mock': "@image('100x100')",
        'remark': 'Avatar、icon',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'image|img|photo|pic',
        'matchCase': false,
        'mock': "@image('400x400')",
        'remark': 'image',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*url',
        'matchCase': false,
        'mock': "@url('http')",
        'remark': 'URL',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'nick|user_?name',
        'matchCase': false,
        'mock': '@cname',
        'remark': 'user name、nick name',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'title|name',
        'matchCase': false,
        'mock': '@ctitle',
        'remark': 'title、name',
    },
    {
        'type': [
            'string',
            'integer',
            'number',
        ],
        'matchType': 'regex',
        'matchDetail': 'id|num|code|amount|quantity|price|discount|balance|money',
        'matchCase': false,
        'mock': '@natural(1,100)',
        'remark': 'number',
    },
    {
        'type': [
            'string',
            'integer',
            'number',
        ],
        'matchType': 'regex',
        'matchDetail': 'phone|mobile|tel$',
        'matchCase': false,
        'mock': '@phone',
        'remark': 'phone',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*date',
        'matchCase': false,
        'mock': "@date('yyyy-MM-dd')",
        'remark': 'date (string format)',
    },
    {
        'type': [
            'integer',
            'number',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*date',
        'matchCase': false,
        'mock': "@date('yyyyMMdd')",
        'remark': 'date (number format)',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'created?_?at|updated?_?at|deleted?_?at|.*time',
        'matchCase': false,
        'mock': "@datetime('yyyy-MM-dd HH:mm:ss')",
        'remark': 'date time',
    },
    {
        'type': [
            'integer',
            'number',
        ],
        'matchType': 'regex',
        'matchDetail': 'created?_?at|updated?_?at|deleted?_?at|.*time',
        'matchCase': false,
        'mock': "@datetime('T')",
        'remark': 'timestamp',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'e?mail*',
        'matchCase': false,
        'mock': "@email('qq.com')",
        'remark': 'email',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*province*',
        'matchCase': false,
        'mock': '@province',
        'remark': 'province',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*city*',
        'matchCase': false,
        'mock': '@city',
        'remark': 'city',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*address',
        'matchCase': false,
        'mock': '@address',
        'remark': 'address',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*district',
        'matchCase': false,
        'mock': '@county',
        'remark': 'country',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*ip',
        'matchCase': false,
        'mock': '@ip',
        'remark': 'IP address',
    },
    {
        'type': [
            'integer',
            'number',
        ],
        'matchType': 'wildcard',
        'matchDetail': 'birthday',
        'matchCase': false,
        'mock': "@date('yyyyMMdd')",
        'remark': 'birthday (string format)',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': 'birthday',
        'matchCase': false,
        'mock': "@date('yyyy-MM-dd')",
        'remark': 'birthday (number format)',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'regex',
        'matchDetail': 'gender|sex',
        'matchCase': false,
        'mock': '@pick(["男","女"])',
        'remark': 'sex',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'wildcard',
        'matchDetail': 'description',
        'matchCase': false,
        'mock': '@cparagraph',
        'remark': 'description in Chinese',
    },
    {
        'type': [
            'integer',
            'number',
        ],
        'matchType': 'wildcard',
        'matchDetail': '*',
        'matchCase': false,
        'mock': '@natural(1, 100)',
        'remark': 'number (exclude sting type)',
    },
    {
        'type': [
            'string',
        ],
        'matchType': 'exact',
        'matchDetail': 'base64',
        'matchCase': true,
        'mock': '@base64',
        'remark': 'byte format',
        'format': 'byte',
    },
] as MockRule[];
export default mockRules;
