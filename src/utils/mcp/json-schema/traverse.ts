/* eslint-disable complexity */
/* eslint-disable guard-for-in */
/* eslint-disable max-depth */
/* eslint-disable max-lines */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/no-namespace */
import {getAllExpendKeys} from './utils';

/**
 * Fork from https://github.com/epoberezkin/json-schema-traverse/blob/master/index.js
 *
 * 根据规范只是新增了几个属性
 */
declare namespace traverse {
  interface SchemaObject {
    $id?: string;
    $schema?: string;
    [x: string]: any;
  }

  type Callback<T extends SchemaObject> = (
    schema: T,
    jsonPtr: string,
    rootSchema: T,
    parentJsonPtr?: string,
    parentKeyword?: string,
    parentSchema?: T,
    keyIndex?: string | number,
  ) => void;

  interface Options<T extends SchemaObject> {
    allKeys?: boolean;
    cb?:
      | Callback<T>
      | {
          pre?: Callback<T>;
          post?: Callback<T>;
        };
  }
}

const noop = () => {};

const keywords = {
    additionalItems: true,
    items: true,
    contains: true,
    additionalProperties: true,
    propertyNames: true,
    not: true,
    if: true,
    then: true,
    else: true,
};

const arrayKeywords = {
    items: true,
    allOf: true,
    anyOf: true,
    oneOf: true,
};

function getPropsKeywords() {
    const {expendRefsKey, expendOverridesKey} = getAllExpendKeys();
    return {
        $defs: true,
        definitions: true,
        properties: true,
        patternProperties: true,
        dependencies: true,
        [expendRefsKey]: true,
        [expendOverridesKey]: true,
    };
}

const skipKeywords = {
    default: true,
    enum: true,
    const: true,
    required: true,
    maximum: true,
    minimum: true,
    exclusiveMaximum: true,
    exclusiveMinimum: true,
    multipleOf: true,
    maxLength: true,
    minLength: true,
    pattern: true,
    format: true,
    maxItems: true,
    minItems: true,
    uniqueItems: true,
    maxProperties: true,
    minProperties: true,
};

export default function traverse<T extends traverse.SchemaObject>(
  schema: T,
  opts: traverse.Options<T>,
  cb?: traverse.Callback<T>,
): void;
export default function traverse<T extends traverse.SchemaObject>(
  schema: T,
  cb: traverse.Callback<T>,
): void;
export default function traverse<T extends traverse.SchemaObject>(
    schema: T,
    opts: traverse.Options<T> | traverse.Callback<T>,
    cb?: traverse.Callback<T>
) {
    let pre = cb || noop;
    let options: traverse.Options<T> = {};
    let post: traverse.Callback<T> = noop;

    if (typeof opts === 'function') {
        pre = opts;
        options = {};
    } else if (typeof opts === 'object') {
        options = opts;
        if (typeof opts.cb === 'function') {
            pre = opts.cb;
        } else if (typeof opts.cb === 'object') {
            pre = opts.cb.pre || noop;
            post = opts.cb.post || noop;
        }
    }

    traverseCore(options, pre, post, schema, '', schema);
}

function escapeJsonPtr(str: string) {
    return str.replace(/~/g, '~0').replace(/\//g, '~1');
}

function traverseCore<T extends traverse.SchemaObject>(
    opts: traverse.Options<T>,
    pre: traverse.Callback<T>,
    post: traverse.Callback<T>,
    schema: T,
    jsonPtr: string,
    rootSchema: T,
    parentJsonPtr?: string,
    parentKeyword?: Extract<keyof T, string>,
    parentSchema?: T,
    keyIndex?: number | Extract<keyof T[Extract<keyof T, string>], string>
) {
    const propsKeywords = getPropsKeywords();
    if (schema && typeof schema === 'object' && !Array.isArray(schema)) {
        pre(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);
        for (const key in schema) {
            const sch = schema[key];
            if (Array.isArray(sch)) {
                if (key in arrayKeywords) {
                    for (let i = 0; i < sch.length; i++)
                    {traverseCore(
                        opts,
                        pre,
                        post,
                        sch[i],
                        jsonPtr + '/' + key + '/' + i,
                        rootSchema,
                        jsonPtr,
                        key,
                        schema,
                        i
                    );}
                }
            } else if (key in propsKeywords) {
                if (sch && typeof sch === 'object') {
                    for (const prop in sch)
                    {traverseCore(
                        opts,
                        pre,
                        post,
                        sch[prop],
                        jsonPtr + '/' + key + '/' + escapeJsonPtr(prop),
                        rootSchema,
                        jsonPtr,
                        key,
                        schema,
                        prop
                    );}
                }
            } else if (key in keywords || (opts.allKeys && !(key in skipKeywords))) {
                traverseCore(opts, pre, post, sch, jsonPtr + '/' + key, rootSchema, jsonPtr, key, schema);
            }
        }
        post(schema, jsonPtr, rootSchema, parentJsonPtr, parentKeyword, parentSchema, keyIndex);
    }
}
