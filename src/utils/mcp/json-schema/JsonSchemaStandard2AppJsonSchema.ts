/* eslint-disable max-lines */
/* eslint-disable max-statements */
/* eslint-disable complexity */
/* eslint-disable max-statements-per-line */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable no-empty */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-param-reassign */
import {Immer} from 'immer';
import type {JSONSchema4, JSONSchema7} from 'json-schema';
import traverse from './traverse';
import type {JSONSchema} from './type';
import {getAllExpendKeys, getMainType} from './utils';

const {produce} = new Immer({
    autoFreeze: false,
});
export const defaultOneOfSchema: () => JSONSchema = () => ({
    type: 'object',
    additionalProperties: false,
});

/**
 * 将 JsonSchema4 -> JsonSchema 扩展
 *
 * @param schema
 * @returns
 */
export function JsonSchemaStandard2AppJsonSchema(schema: JSONSchema7): JSONSchema {
    const {expend$RefsKey, expendOrdersKey, expendIgnorePropertiesKey} = getAllExpendKeys();
    return produce(schema, (draft: JSONSchema) => {
        traverse(draft, (subSchema: JSONSchema) => {
            // required 属性按照规范进行调整，typescript 定义中，多了一个 false 的情况
            if (Array.isArray(subSchema.required)) {
                if (subSchema.required.length === 0) {
                    delete subSchema.required;
                }
            } else if (subSchema.required !== undefined) {
                delete subSchema.required;
            }
            let mainType = getMainType(subSchema);
            if (subSchema[expend$RefsKey]) {
                subSchema.$ref = subSchema[expend$RefsKey];
                delete subSchema.type;
                delete subSchema.properties;
                mainType = 'ref';

                delete subSchema.required;
                delete subSchema[expend$RefsKey];

                delete subSchema[expendIgnorePropertiesKey];
            }

            ['oneOf', 'anyOf', 'allOf'].forEach(key => {
                if (Array.isArray(subSchema[key])) {
                    const filterArray = subSchema[key].filter(Boolean);
                    if (filterArray.length === 0) {
                        subSchema[key] = [defaultOneOfSchema(), defaultOneOfSchema()];
                    } else {
                        subSchema[key] = filterArray;
                    }
                }
            });

            // fix openapi 3.0 nullable spec 2 json schema 7
            if ((subSchema as any).nullable === true) {
                if (subSchema.type) {
                    const type = (Array.isArray(subSchema.type) ? subSchema.type : [subSchema.type]).filter(
                        v => v !== 'null'
                    );
                    subSchema.type = [...Array.from(new Set(type)), 'null'];
                } else if (subSchema.$ref) {
                    delete (subSchema as any).nullable;
                    const collectSubSchema = {...subSchema};
                    // emptyAll
                    Object.keys(subSchema).forEach(k => {
                        delete subSchema[k];
                    });
                    subSchema.oneOf = [
                        collectSubSchema,
                        {
                            type: 'null',
                        },
                    ];
                } else {
                    subSchema.type = 'null';
                }
            }
            if ((subSchema as any).nullable !== undefined) {
                delete (subSchema as any).nullable;
            }

            if (mainType === 'object') {
                if (subSchema.properties) {
                    subSchema[expendIgnorePropertiesKey]?.forEach((v: any) => {
                        delete subSchema.properties?.[v];
                    });
                }
                if (subSchema.required && Array.isArray(subSchema.required)) {
                    subSchema.required = subSchema.required.filter(v => {
                        return subSchema.properties && v in subSchema.properties;
                    });
                    if (subSchema.required.length === 0) {delete subSchema.required;}
                }
                delete subSchema[expendIgnorePropertiesKey];

                subSchema[expendOrdersKey] = getActiveXAppOrder(subSchema);
                // subSchema.properties = reOrderProps(subSchema);
            }
            if (mainType === 'ref') {
                delete subSchema.type;
            }
            // TODO 补充 json4 -> json7 的升级代码

            if ((subSchema as unknown as JSONSchema4).exclusiveMaximum === true) {
                if (subSchema.maximum !== undefined) {
                    subSchema.exclusiveMaximum = subSchema.maximum;
                } else {
                    delete subSchema.exclusiveMaximum;
                }
            } else if ((subSchema as unknown as JSONSchema4).exclusiveMaximum === false) {
                delete subSchema.exclusiveMaximum;
            } else if (typeof subSchema.exclusiveMaximum === 'number') {
                subSchema.maximum = subSchema.exclusiveMaximum;
            }

            if ((subSchema as unknown as JSONSchema4).exclusiveMinimum === true) {
                if (subSchema.minimum !== undefined) {
                    subSchema.exclusiveMinimum = subSchema.minimum;
                } else {
                    delete subSchema.exclusiveMinimum;
                }
            } else if ((subSchema as unknown as JSONSchema4).exclusiveMinimum === false) {
                delete subSchema.exclusiveMinimum;
            } else if (typeof subSchema.exclusiveMinimum === 'number') {
                subSchema.minimum = subSchema.exclusiveMinimum;
            }

            // 将example塞到examples中
            const example = (subSchema as any).example;
            if (example !== undefined) {
                if (Array.isArray(subSchema.examples)) {
                    if (!subSchema.examples.includes(example)) {
                        subSchema.examples.push(example);
                    }
                } else {
                    subSchema.examples = [example];
                }
            }
            delete (subSchema as any).example;

            // enum和const互斥，enum优先
            if (Array.isArray(subSchema.enum)) {
                delete subSchema.const;
            }
        });
    }) as JSONSchema;
}

export function getActiveXAppOrder(schema: JSONSchema): string[] {
    const {expendRefsKey, expendOrdersKey} = getAllExpendKeys();
    const allPropertiesDefaultOrder = Object.keys(schema.properties || {}).concat(
        Object.keys(schema[expendRefsKey] || {})
    );
    const xAppOrders = schema[expendOrdersKey];
    if (!Array.isArray(xAppOrders)) {
        return allPropertiesDefaultOrder;
    }
    // 排除已删除节点
    const pickExistFieldOrder: string[] = Array.from(
        new Set(xAppOrders.filter(v => allPropertiesDefaultOrder.includes(v)))
    );
    // 未包含在 order 的节点
    const restOrder: string[] = Array.from(
        new Set(allPropertiesDefaultOrder.filter(v => !pickExistFieldOrder.includes(v)))
    );
    if (pickExistFieldOrder.length === xAppOrders.length && restOrder.length === 0) {
        return xAppOrders;
    }
    return Array.from(new Set([...pickExistFieldOrder, ...restOrder]));
}

export function reOrderProps(schema: JSONSchema): Record<string, JSONSchema> | undefined {
    return schema.properties;
}
