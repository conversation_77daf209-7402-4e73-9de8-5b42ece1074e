import type {JSONSchema7} from 'json-schema';

export const getFirstSchemaTypeAndNullable = (schema: JSONSchema7): string => {
    let prioritySchemaType = schema.type as string | undefined;
    if (Array.isArray(schema.type)) {
        const hasNullType = schema.type.some((item: any) => item === 'null');
        // @ts-ignore
        // eslint-disable-next-line no-param-reassign
        schema.nullable = hasNullType;
        const realType = schema.type.filter((item: any) => item !== 'null');
        if (realType.length >= 1) {
            [prioritySchemaType] = realType;
        } else {
            prioritySchemaType = 'null';
        }
    }
    return prioritySchemaType ?? '';
};
