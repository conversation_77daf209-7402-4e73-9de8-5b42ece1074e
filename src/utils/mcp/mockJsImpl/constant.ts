/* eslint-disable @typescript-eslint/init-declarations */

import FoxMockJs from './foxMockJs';

export function getMockParams(params: string) {
    let result;
    switch (params) {
        case 'true':
            result = true;
            break;
        case 'false':
            result = false;
            break;
        case 'null':
            result = null;
            break;
        default:
            result = params;
            break;
    }
    return result;
}

export function MockREHandler(reg: string) {
    const regToGen = (FoxMockJs as any).RE.Parser.parse(reg);
    return (FoxMockJs as any).RE.Handler.gen(regToGen);
}
