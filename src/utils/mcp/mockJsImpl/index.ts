/* eslint-disable max-statements-per-line */
/* eslint-disable new-cap */
/* eslint-disable @typescript-eslint/init-declarations */
import {MockFloat, MockImage, MockNow, MockRegexp} from './builtin';
import {getMockParams} from './constant';
import FoxMockJs from './foxMockJs';

function handleBuiltin(data: string, extra: Record<string, any>) {
    let result;
    if (data.startsWith('@regexp')) {
        result = MockRegexp(data);
    }
    if (data.startsWith('@now')) {
        result = MockNow(data, extra);
    }
    if (data.startsWith('@float')) {
        result = MockFloat(data, extra);
    }
    if (data.startsWith('@image')) {
        result = MockImage(data, extra);
    }
    return result;
}

export default function mockJsImpl(data: string, extra: Record<string, any>) {
    const matchBuiltinResult = handleBuiltin(data, extra);
    if (matchBuiltinResult) {return matchBuiltinResult;}
    return FoxMockJs.mock(getMockParams(data));
}
