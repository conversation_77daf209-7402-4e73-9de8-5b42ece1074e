const NormalSize = [
    '300x250',
    '250x250',
    '240x400',
    '336x280',
    '180x150',
    '720x300',
    '468x60',
    '234x60',
    '88x31',
    '120x90',
    '120x60',
    '120x240',
    '125x125',
    '728x90',
    '160x600',
    '120x600',
    '300x600',
];

function pickSize(sizeList: string[]) {
    const index = Math.floor(Math.random() * sizeList.length);
    return sizeList[index];
}

export default function MockImageData(size?: string, text?: string, color?: string) {
    const imgSize = size || pickSize(NormalSize);
    const [inputWidth, inputHeight] = imgSize.split('x');
    const width = parseInt(inputWidth, 10) || 200;
    const height = parseInt(inputHeight, 10) || 100;

    const prefix = 'data:image/svg+xml;charset=UTF-8,';
    const svgString = `<svg xmlns="http://www.w3.org/2000/svg" version="1.1" baseProfile="full" width="${width}" height="${height}"><rect width="100%" height="100%" fill="${
        color ? decodeURIComponent(color) : 'grey'
    }"/><text x="${width / 2}" y="${
        height / 2
    }" font-size="20" alignment-baseline="middle" text-anchor="middle" fill="white">${
        text ? `${text}` : `${width} x ${height}`
    }</text></svg>`;

    return `${prefix}${encodeURIComponent(svgString)}`;
}
