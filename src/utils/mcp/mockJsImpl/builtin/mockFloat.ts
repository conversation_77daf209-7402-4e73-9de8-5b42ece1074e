/* eslint-disable complexity */
/* eslint-disable max-statements-per-line */
import {isNil} from 'lodash';
import FoxMockJs from '../foxMockJs';

export default function MockFloat(data: string, extra: Record<string, any>) {
    if (!extra || !extra.stringArgs) {return FoxMockJs.mock(data);}

    const args = extra.stringArgs;
    if (!isNil(extra.min)) {
        args[0] = extra.min;
    }
    if (!isNil(extra.max)) {
        args[1] = extra.max;
        if (isNil(args[0])) {args[0] = 1;}
    }
    if (!isNil(extra.dmin)) {
        args[2] = extra.dmin;
        if (isNil(args[0])) {args[0] = 1;}
        if (isNil(args[1])) {args[1] = 100;}
    }
    if (!isNil(extra.dmax)) {
        args[3] = extra.dmax;
        if (isNil(args[0])) {args[0] = 1;}
        if (isNil(args[1])) {args[1] = 100;}
        if (isNil(args[2])) {args[2] = 1;}
    }

    const expression = `@float(${args})`;

    const result = FoxMockJs.mock(expression);

    if (result === 0) {
        return '0';
    }

    return result;
}
