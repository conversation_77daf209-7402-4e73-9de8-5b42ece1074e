/* eslint-disable max-statements-per-line */
/* eslint-disable prefer-const */
import dayjs from 'dayjs';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjsTimezone from 'dayjs/plugin/timezone';
import dayjsUtc from 'dayjs/plugin/utc';
import {toSafeInteger} from 'lodash';
import FoxMockJs from '../foxMockJs';

const builtinNowUnitList = ['year', 'month', 'week', 'day', 'hour', 'minute', 'second'];

dayjs.extend(customParseFormat);
dayjs.extend(relativeTime);
dayjs.extend(dayjsUtc);
dayjs.extend(dayjsTimezone);
dayjs.extend(localizedFormat);
dayjs.extend(advancedFormat);

function mockJsFormatToDayJsFormat(format?: string) {
    if (!format) {
        return 'YYYY-MM-DD HH:mm:ss';
    }

    if (format === 'T') {
        return 'x';
    }

    if (typeof format === 'string') {
        return format
            .replace(/yyyy/g, 'YYYY')
            .replace(/yy/g, 'YY')
            .replace(/y/g, 'YY')
            .replace(/dd/g, 'DD')
            .replace(/d/g, 'D')
            .replace(/SS/g, 'SSS');
    }

    return format;
}

export default function MockNow(expression: string, extra: Record<string, any>) {
    let result = FoxMockJs.mock(expression);
    if (!extra || !extra.stringArgs) {return result;}
    let [unit, format] = extra.stringArgs;
    if (!builtinNowUnitList.includes(unit) && !format) {format = unit;}
    format = mockJsFormatToDayJsFormat(format);
    const dayjsResult = (dayjs)(result, format);

    result = dayjsResult;

    if (extra.duration) {
        const [offset, type] = extra.duration.split(' ');
        result = result.add(toSafeInteger(offset), type);
    }

    switch (extra.mode) {
        case 'start':
            result = result.startOf(unit);
            break;
        case 'end':
            result = result.endOf(unit);
            break;
        default:
            break;
    }

    result = result.format(format as string);
    return result;
}
