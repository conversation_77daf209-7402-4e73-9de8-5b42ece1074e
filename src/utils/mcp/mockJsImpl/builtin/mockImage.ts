/* eslint-disable max-statements-per-line */
import {isNil} from 'lodash';
import FoxMockJs from '../foxMockJs';

export default function MockFloat(data: string, extra: Record<string, any>) {
    if (!extra || !extra.stringArgs) {return FoxMockJs.mock(data);}

    const args = extra.stringArgs;
    if (!isNil(extra.size)) {
        args[0] = extra.size;
    }
    if (!isNil(extra.background)) {
        args[1] = extra.background;
        if (isNil(args[0])) {args[0] = '100x100';}
    }
    if (!isNil(extra.foreground)) {
        args[2] = extra.foreground;
    }
    if (!isNil(extra.format)) {
        args[3] = extra.format;
    }
    if (!isNil(extra.text)) {
        args[4] = extra.text;
    }
    const expression = `@image(${args})`;
    return FoxMockJs.mock(expression);
}
