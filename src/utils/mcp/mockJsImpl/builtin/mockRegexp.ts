/* eslint-disable new-cap */
import {<PERSON>ckREHandler} from '../constant';

export default function MockRegexp(data: string) {
    // 支持正则表达式，如 @regexp(/\d+/)
    const matchRegexpResult = /^\s*@regexp\(\s*\/(.+)\/\s*\)\s*$/.exec(data);
    if (matchRegexpResult && matchRegexpResult[1]) {
        try {
            return MockREHandler(matchRegexpResult[1]);
        } catch (e) {
            return '';
        }
    } else {
        return '';
    }
}
