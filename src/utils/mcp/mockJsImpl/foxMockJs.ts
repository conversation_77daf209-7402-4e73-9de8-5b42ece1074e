/* eslint-disable new-cap */
import MockJs from 'mockjs';
import {v4} from 'uuid';
import {MockImageData} from './builtin';
import {Mock<PERSON><PERSON>and<PERSON>} from './constant';

// 扩展 mockjs
MockJs.Random.extend({
    phone() {
        return MockREHandler('1(81|86|98|3\\d)\\d{8}');
    },
    address() {
        return this.province() + this.city() + this.county();
    },
    timestamp(data: string) {
        const time = `${new Date().getTime()}`;
        if (data === 'ms') {
            return time;
        }
        return time.substring(0, time.length - 3);
    },
    qq() {
        return MockREHandler('[1-9][0-9]{4,}');
    },
    gender(data: any) {
        switch (data) {
            case 'male':
                return '男';
            case 'female':
                return '女';
            default:
                return this.pick(['男', '女']);
        }
    },
    landline() {
        return MockREHandler('(0\\d{2}-\\d{8}(-\\d{1,4})?)|(0\\d{3}-\\d{7,8}(-\\d{1,4})?)');
    },
    uuid() {
        return v4();
    },
    dataImage(size?: string, text?: string, color?: string) {
        return MockImageData(size, text, color);
    },
    base64() {
        let result = 'SGVsbG8sIHdvcmxk';
        try {
            result = btoa(this.string(10));
        } catch (error) {
            //
        }
        return result;
    },
});

const FoxMockJs = MockJs;

export default FoxMockJs;
