import {Path} from '@panda-design/path-form';
import {Form, Radio} from 'antd';
import {useMemo} from 'react';

interface Props {
    path?: Path;
    disabled?: boolean;
}

const ProtocolField = ({path = [], disabled}: Props) => {
    const serverSourceType = Form.useWatch('serverSourceType');
    const options = useMemo(
        () => [
            {
                label: 'SSE',
                value: 'SSE',
                hidden: serverSourceType === 'script',
            },
            {
                label: 'STDIO',
                value: 'STDIO',
                hidden: serverSourceType === 'openapi',
            },
            {
                label: 'Streamable HTTP',
                value: 'Streamable_HTTP',
                hidden: serverSourceType === 'openapi' || serverSourceType === 'script',

            },
        ].filter(option => !option.hidden),
        [serverSourceType]
    );
    return (
        <Form.Item
            label="协议"
            name={[...path, 'serverProtocolType']}
            rules={[{required: true, message: '请选择协议类型'}]}
        >
            <Radio.Group options={options} disabled={disabled} />
        </Form.Item>
    );
};

export default ProtocolField;
