/* eslint-disable max-statements */
import {Path} from '@panda-design/path-form';
import {Form} from 'antd';
import {isEmpty, isString, keys} from 'lodash';
import {useCallback, useEffect} from 'react';
import {RuleObject} from 'antd/lib/form';
import ServerConfigContent from './ServerConfigContent';
import {ServerConfigHelpDoc} from './ServerConfigHelpDoc';

interface Props {
    showLabel?: boolean;
    path?: Path;
}

interface STDIOServerConfig {
    command: string;
    args: string[];
}

interface SSEServerConfig {
    url: string;
}

interface StreamableHTTPServerConfig {
    transportType: string;
    url: string;
    disabled?: boolean;
}

interface ServerConfigs {
    mcpServers: {
        [key: string]: STDIOServerConfig | SSEServerConfig | StreamableHTTPServerConfig;
    };
}

const ServerConfigField = ({showLabel = true, path = []}: Props) => {
    const serverConfig = Form.useWatch([...path, 'serverConf', 'serverConfig']);
    const serverProtocolType = Form.useWatch([...path, 'serverProtocolType']);
    const serverKey = Form.useWatch([...path, 'serverKey']);
    const {setFieldValue} = Form.useFormInstance();
    const validator = useCallback(
        // eslint-disable-next-line complexity
        (_: RuleObject, value: string) => {
            try {
                if (!value) {
                    return Promise.reject(new Error('请输入服务器配置'));
                }
                const parsedValue: ServerConfigs = JSON.parse(value?.replace(/\s+/g, ''));
                if (!parsedValue.mcpServers[serverKey]) {
                    return Promise.reject(new Error('请填写正确的标识'));
                }
                if (!parsedValue.mcpServers) {
                    return Promise.reject(new Error('服务器配置格式错误'));
                }
                if (isEmpty(parsedValue.mcpServers)) {
                    return Promise.reject(new Error('服务器配置为空'));
                }
                if (keys(parsedValue).length > 1) {
                    return Promise.reject(new Error('服务器配置格式错误'));
                }
                // 当 serverProtocolType不同时，针对serverConfig有不同的校验。
                if (serverProtocolType === 'STDIO') {
                    const serverConfig = parsedValue.mcpServers[serverKey] as STDIOServerConfig;
                    const validCommand = ['npx', 'uvx'].includes(serverConfig.command);
                    const validArgs = Array.isArray(serverConfig.args)
                        && serverConfig.args.length > 0;
                    const inValid = !validCommand || !validArgs;
                    if (inValid) {
                        return Promise.reject(new Error('STDIO协议下，仅支持npx和uvx命令，且参数不能为空'));
                    }
                }
                if (serverProtocolType === 'SSE') {
                    const serverConfig = parsedValue.mcpServers[serverKey] as SSEServerConfig;
                    const valid = isString(serverConfig.url) && serverConfig.url.trim() !== '';
                    if (!valid) {
                        return Promise.reject(new Error('SSE协议下，server配置中url不能为空'));
                    }
                }
                if (serverProtocolType === 'Streamable_HTTP') {
                    const serverConfig = parsedValue.mcpServers[serverKey] as StreamableHTTPServerConfig;
                    const urlValid = isString(serverConfig.url) && serverConfig.url.trim() !== '';
                    const transportTypeValid =
                        isString(serverConfig.transportType) && serverConfig.transportType === 'streamableHttp';
                    if (!urlValid) {
                        return Promise.reject(new Error('Streamable HTTP协议下，server配置中url不能为空，且url字段需填写服务器提供的端点路径'));
                    }
                    if (!transportTypeValid) {
                        return Promise.reject(new Error('Streamable HTTP协议下，transportType字段需填写streamableHttp'));
                    }
                }
                return Promise.resolve();
            } catch (e) {
                return Promise.reject(new Error('服务器配置格式错误'));
            }
        },
        [serverKey, serverProtocolType]
    );
    useEffect(
        () => {
            if (!isString(serverConfig)) {
                setFieldValue([...path, 'serverConf', 'serverConfig'], JSON.stringify(serverConfig, null, 4));
            } else {
                setFieldValue([...path, 'serverConf', 'serverConfig'], serverConfig);
            }
        },
        [path, serverConfig, setFieldValue]
    );
    return (
        <>
            <Form.Item
                label={showLabel
                    ? <span>服务器配置<ServerConfigHelpDoc serverProtocolType={serverProtocolType} /></span> : null}
                name={[...path, 'serverConf', 'serverConfig']}
                validateTrigger="onBlur"
                required
                rules={[
                    {
                        validateTrigger: 'onBlur',
                        validator: validator,
                    },
                ]}
            >
                <ServerConfigContent basePath={path} />
            </Form.Item>
        </>
    );
};

export default ServerConfigField;
