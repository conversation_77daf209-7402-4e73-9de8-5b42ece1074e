#!/usr/bin/env python3
"""
交互式审查门控脚本
用于在EXECUTE模式中对标记为review:true的清单项目进行用户驱动的迭代审查
"""

import sys

def main():
    print("=== 交互式审查门控已启动 ===")
    print("您现在可以输入子提示来对当前清单项目进行迭代修改。")
    print("输入以下任一关键字来结束审查：")
    print("- TASK_COMPLETE")
    print("- 完成")
    print("- 下一步")
    print("- 继续")
    print("- OK")
    print("- ok")
    print("=" * 37)
    
    try:
        while True:
            user_input = input("请输入子提示或结束关键字: ").strip()
            
            # 检查结束关键字
            end_keywords = [
                'TASK_COMPLETE', '完成', '下一步', 'DONE', '结束', 'FINISH',
                'task_complete', 'done', 'finish', '继续', 'OK', 'ok'
            ]
            
            if user_input.upper() in [kw.upper() for kw in end_keywords]:
                print(f"收到结束关键字: {user_input}")
                print("交互式审查结束。")
                break
            
            if user_input:
                # 输出格式化的用户子提示，供AI监控和处理
                print(f"USER_REVIEW_SUB_PROMPT: {user_input}")
                print("等待AI处理您的子提示...")
            else:
                print("请输入有效的子提示或结束关键字。")
                
    except KeyboardInterrupt:
        print("\n用户中断了交互式审查。")
    except EOFError:
        print("\n输入流结束，交互式审查终止。")
    
    print("交互式审查门控脚本退出。")

if __name__ == "__main__":
    main()
